<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { skuApi, itemApi, unitApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import Unit from '../../unit/index.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Sku | null;
  /** item data */
  item: Api.Wms.Item | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增规格',
    edit: '编辑规格'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Wms.Sku, 'name' | 'code' | 'unit' | 'min' | 'summary' | 'attrs' | 'order' | 'status' | 'itemId'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    unit: '',
    min: 0,
    summary: '',
    attrs: [],
    order: 0,
    status: true,
    itemId: props.item?.id || 0,
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code' | 'unit' | 'itemId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule,
  unit: defaultRequiredRule,
  itemId: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await skuApi.save(model.value) : await skuApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();

    // 初始化单位选项
    getUnits()
  }
});

const units = ref<Api.Wms.Unit[]>([])

async function getUnits() {
  const { data, error } = await unitApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!error) {
    units.value = data.records;
  }
}

const unitVisible = ref(false)
watch(unitVisible, () => {
  if (!unitVisible.value) {
    getUnits()
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="600">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="所属物料" path="itemId">
          <RemoteSelect
            v-model:value="model.itemId"
            label-field="name"
            value-field="id"
            :options="[item]"
            :api-fn="itemApi.list"
            disabled
          />
        </NFormItem>
        <NFormItem label="规格名" path="name">
          <NInput v-model:value="model.name" placeholder="请输入规格名" clearable />
        </NFormItem>
        <NFormItem label="编码" path="code">
          <NInputGroup>
            <NInput v-model:value="model.code" placeholder="请输入编码" clearable />
            <NButton @click="() => model.code = model.name">同上</NButton>
          </NInputGroup>
        </NFormItem>
        <NFormItem label="单位" path="unit">
          <NSelect v-model:value="model.unit" :options="units || []" label-field="name" value-field="name" placeholder="请选择单位">
            <template #action>
              <NButton type="primary" size="small" dashed text block @click="unitVisible = true">
                <template #icon>
                  <icon-ph-squares-four />
                </template>
                管理
              </NButton>
            </template>
          </NSelect>
        </NFormItem>
        <NFormItem label="预警库存" path="min">
          <NInputNumber v-model:value="model.min" :min="0" :precision="2" placeholder="请输入预警库存" clearable />
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
        <NFormItem label="附加属性" path="attrs">
          <NDynamicInput
            v-model:value="model.attrs"
            preset="pair"
            key-field="label"
            key-placeholder="属性名"
            value-placeholder="属性值"
            show-sort-button  />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
      <!-- 单位管理 -->
      <Unit v-model:visible="unitVisible" />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
